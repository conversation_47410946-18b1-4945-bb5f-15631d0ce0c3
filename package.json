{"name": "livecaptions-client", "version": "1.0.0", "description": "Node.js client for LiveCaptions C# tool with server mode support", "main": "nodejs_client_example.js", "scripts": {"start": "node simple_example.js", "test": "node simple_example.js", "example": "node simple_example.js"}, "keywords": ["livecaptions", "speech-to-text", "accessibility", "windows", "automation"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=12.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "your-repo-url"}}