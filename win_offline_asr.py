import argparse
import json
import time
import threading
import sys
import os
from time import sleep
import requests
import uuid
import io
import psutil
import subprocess
from pywinauto.application import Application
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
# Windows API
import ctypes
from ctypes import wintypes

GWL_EXSTYLE = -20
WS_EX_TOOLWINDOW = 0x00000080
SW_MINIMIZE = 6

user32 = ctypes.windll.user32
GetWindowLongPtrW = user32.GetWindowLongPtrW
SetWindowLongPtrW = user32.SetWindowLongPtrW
ShowWindow = user32.ShowWindow
from pywinauto.keyboard import send_keys

LIVECAPTIONS_PATH = "LiveCaptions.exe"
app = None
window = None
process = None

# ========== 基础功能 ==========
def get_process_id_by_name(process_name):
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] == process_name:
            return proc.pid
    return None

def is_livecaptions_running():
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] and proc.info['name'].lower() == 'livecaptions.exe':
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    return False

def start_livecaptions():
    global process, app, window
    try:
        pid = get_process_id_by_name(LIVECAPTIONS_PATH)
        if pid is None:
            process = subprocess.Popen(LIVECAPTIONS_PATH, shell=True)
            # 智能等待进程启动，而不是固定延迟
        app = Application(backend='uia').connect(path=LIVECAPTIONS_PATH)
        window = app.top_window()
        # 等待窗口准备就绪
        window.wait('exists ready', timeout=10)
        return True
    except Exception as e:
        return False

def stop_livecaptions():
    try:
        pid = get_process_id_by_name(LIVECAPTIONS_PATH)
        if pid:
            os.system(f'taskkill /F /PID {pid}')
            return True
        return False
    except Exception:
        return False

def connect_window():
    global app, window
    try:
        app = Application(backend='uia').connect(path=LIVECAPTIONS_PATH)
        window = app.top_window()
        return True
    except Exception:
        return False

# ========== 控制窗口 ==========
def hide_window():
    global window
    if not window:
        if not connect_window():
            return False
    try:
        hWnd = window.handle
        exStyle = user32.GetWindowLongPtrW(hWnd, GWL_EXSTYLE)
        user32.ShowWindow(hWnd, SW_MINIMIZE)
        new_exStyle = exStyle | WS_EX_TOOLWINDOW
        user32.SetWindowLongPtrW(hWnd, GWL_EXSTYLE, new_exStyle)
        return True
    except Exception:
        return False

def show_window():
    global window
    if not window:
        if not connect_window():
            start_livecaptions()
            return True
    try:
        window.restore()
        return True
    except Exception:
        return False

def get_window_status():
    """
    检测Live Caption窗口是否显示
    返回True表示窗口显示，False表示窗口隐藏或不存在
    类似于C#中的 Translator.Window.Current.BoundingRectangle == Rect.Empty 检测
    """
    global window
    if not window:
        if not connect_window():
            return False

    try:
        # 检查窗口是否存在且可见
        if not window.exists():
            return False

        # 检查窗口是否可见
        if not window.is_visible():
            return False

        # 检查窗口边界矩形是否为空（类似C#的BoundingRectangle检测）
        rect = window.rectangle()
        if rect.width() <= 0 or rect.height() <= 0:
            return False

        # 检查窗口是否最小化
        if window.is_minimized():
            return False

        return True

    except Exception as e:
        print(f"检测窗口状态时发生错误: {e}", file=sys.stderr)
        return False

# ========== 字幕内容 ==========
def get_caption_text():
    global window
    if not window:
        if not connect_window():
            return None
    try:
        captions_text_element = window.child_window(auto_id="CaptionsScrollViewer", control_type="Pane")
        if captions_text_element.exists():
            return captions_text_element.window_text()
        return None
    except Exception:
        return None

def watch_caption_text(interval=0.5):
    last_text = None
    try:
        while True:
            text = get_caption_text()
            if text != last_text:
                print(json.dumps({"caption": text},ensure_ascii=False), flush=True)
                last_text = text
            time.sleep(interval)  # 保留此处的sleep，因为这是监控循环的必要间隔
    except KeyboardInterrupt:
        pass

# ========== 语言设置与获取 ==========
def set_language_by_prefix(language_prefix):
    global app, window
    if not window:
        if not connect_window():
            return False
    try:
        # 首先确保所有菜单都关闭
        try:
            send_keys("{ESC}")
            time.sleep(0.1)
        except Exception:
            pass

        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        settings_button.wait('exists enabled', timeout=5)
        settings_button.click()

        # 智能等待弹出菜单出现 - 使用class_name而不是依赖文本标题
        change_language_item = None
        try:
            # 通过class_name查找弹出窗口，避免依赖本地化文本
            popup_pane = window.child_window(class_name="Xaml_WindowedPopupClass", control_type="Pane")
            popup_pane.wait('exists visible', timeout=3)
            change_language_item = popup_pane.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")
        except Exception:
            pass
        if change_language_item is None or not change_language_item.exists():
            change_language_item = window.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")

        change_language_item.wait('exists enabled', timeout=3)
        change_language_item.invoke()

        # 智能等待语言列表出现
        language_list = window.child_window(auto_id="SpeechModelDropDown", control_type="ComboBox")
        language_list.wait('exists enabled', timeout=5)
        language_list.expand()

        # 智能等待列表展开完成
        language_list.wait('ready', timeout=3)
        language_list.set_focus()

        # 改进的语言查找策略：支持滚动查找
        found = False
        seen_items = set()

        # 先从顶部开始查找
        send_keys("{HOME}")
        # 短暂等待滚动完成
        time.sleep(0.1)

        # 尝试多种滚动策略查找目标语言
        max_attempts = 50
        for attempt in range(max_attempts):
            # 获取当前可见的项目
            items = language_list.descendants(control_type="ListItem")
            current_items = []

            for item in items:
                item_text = item.window_text().strip()
                if item_text:
                    current_items.append((item_text, item))

                    # 检查是否匹配目标语言前缀
                    if item_text.startswith(language_prefix):
                        print(f"找到匹配的语言: {item_text}",file=sys.stderr)
                        try:
                            item.select()
                            # 智能等待选择完成，而不是固定延迟
                            try:
                                item.wait('ready', timeout=2)
                            except:
                                time.sleep(0.2)  # 备用短暂延迟
                            found = True
                            break
                        except Exception as e:
                            print(f"选择语言项失败: {e}",file=sys.stderr)
                            continue

            if found:
                break

            # 检查是否有新的项目，如果没有新项目说明已经到底了
            current_texts = set(text for text, _ in current_items)
            if current_texts.issubset(seen_items):
                # 没有新项目，尝试向下滚动
                send_keys("{PGDN}")
                time.sleep(0.05)  # 减少滚动延迟
                # 再次检查，如果还是没有新项目，说明已经到底了
                items = language_list.descendants(control_type="ListItem")
                new_texts = set(item.window_text().strip() for item in items if item.window_text().strip())
                if new_texts.issubset(seen_items):
                    break

            seen_items.update(current_texts)

            # 向下滚动几个项目
            send_keys("{DOWN 3}")
            time.sleep(0.05)  # 减少滚动延迟

        if found:
            # 找到并选择了语言，点击完成按钮 - 使用auto_id避免国际化问题
            try:
                # 优先使用auto_id查找继续按钮
                continue_btn = window.child_window(auto_id="ContinueButton", control_type="Button")
                if continue_btn.exists() and continue_btn.is_enabled():
                    continue_btn.click()
                else:
                    # 备用方案：使用键盘操作
                    send_keys("{ENTER}")
            except Exception:
                # 最后的备用方案：ESC键退出
                try:
                    send_keys("{ENTER}")
                except Exception:
                    pass
            return True
        else:
            print(f"未找到匹配的语言: {language_prefix}",file=sys.stderr)
            # 关闭设置窗口
            try:
                settings_button.click()
            except Exception:
                pass
            return False

    except Exception as e:
        print(f"设置语言时发生错误: {e}",file=sys.stderr)
        return False

def list_languages():
    global app, window
    if not window:
        if not connect_window():
            return []

    try:
        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        settings_button.wait('exists enabled', timeout=5)
        settings_button.click()

        # 找到“更改语言”菜单项并点击
        change_language_item = window.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")
        change_language_item.wait('exists enabled', timeout=3)
        change_language_item.invoke()
        # 智能等待语言列表出现

        # 智能等待并找到 ComboBox
        language_list = window.child_window(auto_id="SpeechModelDropDown", control_type="ComboBox")
        language_list.wait('exists enabled', timeout=5)
        language_list.expand()
        language_list.wait('ready', timeout=3)
        language_list.set_focus()

        # 改进的滚动策略：使用多种方法快速收集所有语言
        seen = set()
        langs = []

        # 方法1: 先尝试直接获取所有可见项目
        try:
            items = language_list.descendants(control_type="ListItem")
            for item in items:
                text = item.window_text().strip()
                if text and text not in seen:
                    seen.add(text)
                    langs.append(text)
        except Exception:
            pass

        # 方法2: 使用 End 键快速跳到底部，然后向上收集
        try:
            send_keys("{END}")
            time.sleep(0.1)  # 减少滚动延迟

            # 从底部开始向上收集
            for _ in range(50):  # 最多尝试50次向上
                items = language_list.descendants(control_type="ListItem")
                new_items = [item.window_text().strip() for item in items if item.window_text().strip()]
                added = False
                for text in new_items:
                    if text and text not in seen:
                        seen.add(text)
                        langs.append(text)
                        added = True

                if not added:
                    break

                send_keys("{UP 5}")  # 一次向上5项，加快速度
                time.sleep(0.05)  # 减少滚动延迟
        except Exception:
            pass

        # 方法3: 如果上述方法收集不全，使用 Page Down 快速滚动
        if len(langs) < 10:  # 如果语言数量太少，可能没收集全
            try:
                send_keys("{HOME}")  # 回到顶部
                time.sleep(0.1)  # 减少滚动延迟

                same_count = 0
                while same_count < 3:  # 连续3次没新增就停止
                    items = language_list.descendants(control_type="ListItem")
                    new_items = [item.window_text().strip() for item in items if item.window_text().strip()]
                    added = False
                    for text in new_items:
                        if text and text not in seen:
                            seen.add(text)
                            langs.append(text)
                            added = True

                    if not added:
                        same_count += 1
                    else:
                        same_count = 0

                    send_keys("{PGDN}")  # 使用 Page Down 快速滚动
                    time.sleep(0.1)  # 减少滚动延迟
            except Exception:
                pass

        # 关闭设置窗口（如有“完成”按钮就点掉）
        # 确保完全关闭设置菜单
        try:
            # 首先尝试关闭语言下拉列表
            language_list.collapse()
        except Exception:
            pass

        # 尝试点击完成或继续按钮 - 使用auto_id避免国际化问题
        try:
            # 优先使用auto_id查找继续按钮
            continue_btn = window.child_window(auto_id="ContinueButton", control_type="Button")
            if continue_btn.exists() and continue_btn.is_enabled():
                continue_btn.click()
                time.sleep(0.1)  # 短暂等待按钮响应
            else:
                # 备用方案：使用键盘操作
                send_keys("{ENTER}")
                time.sleep(0.1)
        except Exception:
            # 最后的备用方案
            try:
                send_keys("{ENTER}")
                time.sleep(0.1)
            except Exception:
                pass

        # 确保设置菜单完全关闭
        try:
            # 按 ESC 键关闭任何打开的菜单
            send_keys("{ESC}")
            time.sleep(0.1)
        except Exception:
            pass

        # 最后尝试点击设置按钮关闭菜单
        try:
            settings_button.click()
            time.sleep(0.1)
        except Exception:
            pass

        return langs

    except Exception as e:
        print("Exception:", e,file=sys.stderr)
        return []


def list_languages_fast():
    """
    快速获取语言列表的备选方案
    使用更激进的滚动策略
    """
    global app, window
    if not window:
        if not connect_window():
            return []

    try:
        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        settings_button.wait('exists enabled', timeout=5)
        settings_button.click()
        # 智能等待菜单出现

        # 智能等待并找到"更改语言"菜单项 - 使用class_name避免国际化问题
        change_language_item = None
        try:
            # 通过class_name查找弹出窗口，避免依赖本地化文本
            popup_pane = window.child_window(class_name="Xaml_WindowedPopupClass", control_type="Pane")
            popup_pane.wait('exists visible', timeout=3)
            change_language_item = popup_pane.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")
        except Exception:
            pass
        if change_language_item is None or not change_language_item.exists():
            change_language_item = window.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")

        change_language_item.wait('exists enabled', timeout=3)
        change_language_item.invoke()
        # 智能等待语言列表出现

        # 智能等待并找到 ComboBox
        language_list = window.child_window(auto_id="SpeechModelDropDown", control_type="ComboBox")
        language_list.wait('exists enabled', timeout=5)
        language_list.expand()
        language_list.wait('ready', timeout=3)
        language_list.set_focus()

        # 超快速滚动策略
        seen = set()
        langs = []

        # 直接跳到底部，然后快速向上扫描
        send_keys("{CTRL+END}")  # 尝试跳到最底部
        time.sleep(0.1)  # 减少滚动延迟

        # 快速向上扫描，每次移动多个项目
        for i in range(20):  # 最多20次大幅向上移动
            items = language_list.descendants(control_type="ListItem")
            new_items = [item.window_text().strip() for item in items if item.window_text().strip()]

            for text in new_items:
                if text and text not in seen:
                    seen.add(text)
                    langs.append(text)

            # 大幅向上移动
            send_keys("{PGUP}")
            time.sleep(0.05)  # 减少滚动延迟

            # 如果已经到顶部，停止
            try:
                send_keys("{UP}")
                time.sleep(0.02)  # 最小滚动延迟
                current_items = language_list.descendants(control_type="ListItem")
                current_texts = [item.window_text().strip() for item in current_items if item.window_text().strip()]
                if set(current_texts) == set(new_items):  # 没有变化，说明到顶了
                    break
            except Exception:
                break

        # 确保完全关闭设置菜单
        try:
            # 首先尝试关闭语言下拉列表
            language_list.collapse()
        except Exception:
            pass

        # 尝试点击完成或继续按钮 - 使用auto_id避免国际化问题
        try:
            # 优先使用auto_id查找继续按钮
            continue_btn = window.child_window(auto_id="ContinueButton", control_type="Button")
            if continue_btn.exists() and continue_btn.is_enabled():
                continue_btn.click()
                time.sleep(0.1)  # 短暂等待按钮响应
            else:
                # 备用方案：使用键盘操作
                send_keys("{ENTER}")
                time.sleep(0.1)
        except Exception:
            # 最后的备用方案
            try:
                send_keys("{ENTER}")
                time.sleep(0.1)
            except Exception:
                pass

        # 确保设置菜单完全关闭
        try:
            # 按 ESC 键关闭任何打开的菜单
            send_keys("{ESC}")
            time.sleep(0.1)
        except Exception:
            pass

        # 最后尝试点击设置按钮关闭菜单
        try:
            settings_button.click()
            time.sleep(0.1)
        except Exception:
            pass

        return langs

    except Exception as e:
        print("Exception in list_languages_fast:", e,file=sys.stderr)
        return []


def translate_languages_to_english(languages, api_key=None):
    """
    使用微软翻译API将语言列表翻译成英文

    Args:
        languages: 语言名称列表
        api_key: 微软翻译API密钥（可选，如果不提供则使用默认密钥）

    Returns:
        dict: 原语言名称到英文翻译的映射字典
    """
    if not languages:
        return {}

    # 构建请求数据
    request_data = [{"text": lang} for lang in languages]

    # API端点
    url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&to=en"

    # 使用提供的API密钥或默认密钥
    subscription_key = api_key or "f2cb3ca133364ba18781f24a15fc9c8d"

    # 生成10位随机数作为ClientTraceId
    client_trace_id = str(uuid.uuid4()).replace('-', '')[:10]

    # 请求头
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Ocp-Apim-Subscription-Key': subscription_key,
        'Ocp-Apim-Subscription-Region': 'centralus',
        'X-ClientTraceId': client_trace_id
    }

    try:
        # 发送请求
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        response.raise_for_status()

        # 解析响应
        translations = response.json()

        # 构建映射字典
        result = {}
        for i, translation in enumerate(translations):
            if i < len(languages):
                original = languages[i]
                translated = translation['translations'][0]['text']
                result[original] = translated

        return result

    except Exception as e:
        print(f"translate error : {e}",file=sys.stderr)
        return {}


def list_languages_with_translation(api_key=None, use_fast=False):
    """
    获取语言列表并翻译成英文

    Args:
        api_key: 微软翻译API密钥
        use_fast: 是否使用快速获取方法

    Returns:
        dict: 包含原始语言列表和翻译映射的字典
    """
    # 获取语言列表
    if use_fast:
        languages = list_languages_fast()
    else:
        languages = list_languages()

    if not languages:
        return {"languages": [], "translations": {}}

    # 翻译语言名称
    translations = translate_languages_to_english(languages, api_key)

    return {
        "languages": languages,
        "translations": translations
    }


def set_mic_audio_checkbox(enable=True):
    global app, window
    if not window:
        if not connect_window():
            return False

    try:
        # 点击设置按钮
        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        if not settings_button.exists(timeout=5):
            print("设置按钮不存在",file=sys.stderr)
            return False

        settings_button.click()

        # 使用显式等待替代固定的time.sleep - 使用class_name避免国际化问题
        try:
            # 通过class_name查找弹出窗口，避免依赖本地化文本
            popup_pane = window.child_window(class_name="Xaml_WindowedPopupClass", control_type="Pane")
            popup_pane.wait('exists enabled visible', timeout=5)
        except Exception as e:
            print(f"无法找到弹出窗口面板: {e}",file=sys.stderr)
            settings_button.click()  # 关闭菜单
            return False

        # 悬停到首选项菜单项
        preferences_item = popup_pane.child_window(auto_id="PreferencesButton", control_type="MenuItem")
        if not preferences_item.exists(timeout=3):
            print("首选项菜单项不存在",file=sys.stderr)
            settings_button.click()
            return False

        preferences_item.click_input()
        # 智能等待首选项菜单出现
        time.sleep(0.2)  # 最小必要延迟
        # 打印控件结构用于调试
        # try:
        #     popup_pane.print_control_identifiers()
        # except Exception as e:
        #     print(f"无法打印控件标识符: {e}")

        # 改进控件定位逻辑
        try:
            # 尝试定位菜单
            menu = popup_pane.child_window(auto_id="SettingsMenuFlyout", control_type="Menu")
            menu.wait('exists enabled visible', timeout=3)
            # menu.print_control_identifiers()
            # 定位目标菜单项
            target_item = menu.child_window(auto_id="MicrophoneMenuFlyoutItem", control_type="MenuItem")
            target_item.wait('exists enabled visible', timeout=3)
            # 直接点击按钮，不检查状态（因为它不是开关控件）
            # target_item.print_control_identifiers()
            # print(target_item.get_properties())
            children = target_item.children()
            for c in children:
                print(c.window_text(),file=sys.stderr)

            target_item.click_input()
            target_item.click_input()
            print(f"已{'启用' if enable else '禁用'}麦克风音频",file=sys.stderr)

            return True

        except Exception as e:
            print(f"定位麦克风菜单项时出错: {e}",file=sys.stderr)
            # 打印详细的控件结构，帮助调试
            try:
                print("\n详细控件结构:",file=sys.stderr)
                popup_pane.dump_tree()
            except:
                pass
            return False

    except Exception as e:
        print(f"设置麦克风音频时发生未知错误: {e}",file=sys.stderr)
        return False

# ========== 命令行接口 ==========
def main():
    parser = argparse.ArgumentParser(description="Control LiveCaptions via CLI")
    subparsers = parser.add_subparsers(dest="command")

    subparsers.add_parser("start", help="Start LiveCaptions")
    subparsers.add_parser("stop", help="Stop LiveCaptions")
    subparsers.add_parser("hide", help="Hide LiveCaptions window")
    subparsers.add_parser("show", help="Show LiveCaptions window")
    subparsers.add_parser("status", help="Check if LiveCaptions window is visible")
    get_caption_parser = subparsers.add_parser("get_caption", help="Get current caption text")
    get_caption_parser.add_argument("--watch", action="store_true", help="Watch captions in real time")
    set_lang_parser = subparsers.add_parser("set_language", help="Set language by prefix")
    set_lang_parser.add_argument("--lang", required=True, help="Language prefix, e.g. '简体中文' or '英语'")
    list_lang_parser = subparsers.add_parser("list_languages", help="List available languages (with translation by default)")
    list_lang_parser.add_argument("--fast", action="store_true", help="Use fast scrolling method")
    list_lang_parser.add_argument("--no-translate", action="store_true", help="Do not translate language names to English")
    list_lang_parser.add_argument("--api-key", help="Microsoft Translator API key (optional, uses default if not provided)")
    subparsers.add_parser("enable_mic_audio", help="Enable '包含麦克风音频' checkbox in preferences")
    subparsers.add_parser("disable_mic_audio", help="Disable '包含麦克风音频' checkbox in preferences")

    args = parser.parse_args()

    if args.command == "start":
        ok = start_livecaptions()
        print(json.dumps({"success": ok}))
    elif args.command == "stop":
        ok = stop_livecaptions()
        print(json.dumps({"success": ok}))
    elif args.command == "hide":
        ok = hide_window()
        print(json.dumps({"success": ok}))
    elif args.command == "show":
        ok = show_window()
        print(json.dumps({"success": ok}))
    elif args.command == "status":
        is_visible = get_window_status()
        print(json.dumps({"visible": is_visible}))
    elif args.command == "get_caption":
        if args.watch:
            watch_caption_text()
        else:
            text = get_caption_text()
            print(json.dumps({"caption": text}))
    elif args.command == "set_language":
        ok = set_language_by_prefix(args.lang)
        print(json.dumps({"success": ok}))
    elif args.command == "list_languages":
        # 默认进行翻译，除非明确指定不翻译
        if hasattr(args, 'no_translate') and args.no_translate:
            # 不翻译，只返回原始语言列表
            if hasattr(args, 'fast') and args.fast:
                langs = list_languages_fast()
            else:
                langs = list_languages()
            print(json.dumps({"languages": langs}, ensure_ascii=False))
        else:
            # 默认使用翻译功能
            api_key = getattr(args, 'api_key', None)
            use_fast = hasattr(args, 'fast') and args.fast
            result = list_languages_with_translation(api_key, use_fast)
            print(json.dumps(result, ensure_ascii=False))
    elif args.command == "enable_mic_audio":
        ok = set_mic_audio_checkbox(True)
        print(json.dumps({"success": ok}))
    elif args.command == "disable_mic_audio":
        ok = set_mic_audio_checkbox(False)
        print(json.dumps({"success": ok}))
    else:
        parser.print_help()

if __name__ == "__main__":
    main()