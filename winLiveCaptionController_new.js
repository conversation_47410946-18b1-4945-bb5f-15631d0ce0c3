/**
 * Windows Live Caption Controller - Server Mode Version
 * 使用LiveCaptionsClient进行高性能通信
 */

import { app, ipcMain } from 'electron'
import path from 'path'
const LiveCaptionsClient = require('./nodejs_client_example');

export default class WinLiveCaptionController {
  constructor(mainWindow) {
    this.mainWindow = mainWindow // Main window reference
    this.client = null // LiveCaptionsClient instance
    this.isConnected = false // Connection status
    this.isWatchingCaptions = false // Watch status
    this.watchCallback = null // Monitoring callback function
  }

  /**
   * 初始化连接到server模式
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async initializeConnection() {
    if (this.isConnected && this.client) {
      return { success: true, message: 'Already connected' }
    }

    try {
      const exePath = this.getControllerPath()
      if (!exePath) {
        return { success: false, message: 'Current platform does not support Windows Live Caption control' }
      }

      this.client = new LiveCaptionsClient(exePath)
      
      // 设置字幕更新回调
      this.client.on('captionUpdate', (caption) => {
        if (this.watchCallback) {
          this.watchCallback({ caption })
        }
        // 发送到渲染进程
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('winLiveCaption-captionUpdate', { caption })
        }
      })

      await this.client.connect()
      this.isConnected = true
      
      console.log('✅ LiveCaptionsClient connected successfully')
      return { success: true, message: 'Connected to LiveCaptions server' }
    } catch (error) {
      console.error('❌ Failed to connect to LiveCaptions server:', error)
      this.isConnected = false
      return { success: false, message: 'Failed to connect: ' + error.message }
    }
  }

  /**
   * 确保连接已建立
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async ensureConnection() {
    if (!this.isConnected || !this.client) {
      return await this.initializeConnection()
    }
    return { success: true, message: 'Connection already established' }
  }

  /**
   * Start Live Caption
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async startLiveCaption() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.startLiveCaptions()
      return result
    } catch (error) {
      console.error('Failed to start Live Caption:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Stop Live Caption
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async stopLiveCaption() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.stopLiveCaptions()
      return result
    } catch (error) {
      console.error('Failed to stop Live Caption:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Hide Live Caption window
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async hideLiveCaption() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.hideWindow()
      return result
    } catch (error) {
      console.error('Failed to hide Live Caption:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Show Live Caption window
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async showLiveCaption() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.showWindow()
      return result
    } catch (error) {
      console.error('Failed to show Live Caption:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Get current caption content
   * @returns {Promise<{success: boolean, caption?: string, message?: string}>}
   */
  async getCurrentCaption() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.getCaption()
      return result
    } catch (error) {
      console.error('Failed to get current caption:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Set recognition language
   * @param {string} language - Language prefix, such as "简体中文", "English", "日语", etc.
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async setLanguage(language) {
    if (!language || typeof language !== 'string') {
      return { success: false, message: 'Language parameter cannot be empty' }
    }

    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.setLanguage(language)
      return result
    } catch (error) {
      console.error('Failed to set language:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Get available language list
   * @param {object} options - Options
   * @param {boolean} options.fast - Whether to use fast scrolling method
   * @param {boolean} options.noTranslate - Whether not to translate language names
   * @param {string} options.apiKey - Microsoft Translator API key (optional)
   * @returns {Promise<{success: boolean, languages?: string[], translations?: object, message?: string}>}
   */
  async getLanguageList(options = {}) {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.listLanguages(options.fast, options.noTranslate, options.apiKey)
      return result
    } catch (error) {
      console.error('Failed to get language list:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Enable microphone audio
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async enableMicAudio() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.enableMicAudio()
      return result
    } catch (error) {
      console.error('Failed to enable mic audio:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Disable microphone audio
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async disableMicAudio() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.disableMicAudio()
      return result
    } catch (error) {
      console.error('Failed to disable mic audio:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Check if Live Caption window is visible
   * @returns {Promise<{success: boolean, isVisible?: boolean, message?: string}>}
   */
  async isLiveCaptionVisible() {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.getStatus()
      return result
    } catch (error) {
      console.error('Failed to check visibility:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Start real-time caption monitoring (多线程模式)
   * @param {function} callback - Callback function when caption updates
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async startWatchCaption(callback) {
    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    if (this.isWatchingCaptions) {
      return { success: false, message: 'Caption monitoring is already active' }
    }

    try {
      this.watchCallback = callback
      const result = await this.client.startWatch()
      
      if (result.success) {
        this.isWatchingCaptions = true
        console.log('✅ Caption monitoring started (multi-threaded mode)')
      }
      
      return result
    } catch (error) {
      console.error('Failed to start caption monitoring:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Stop real-time caption monitoring
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async stopWatchCaption() {
    if (!this.isWatchingCaptions) {
      return { success: true, message: 'Caption monitoring is not active' }
    }

    const connectionResult = await this.ensureConnection()
    if (!connectionResult.success) {
      return connectionResult
    }

    try {
      const result = await this.client.stopWatch()
      
      if (result.success) {
        this.isWatchingCaptions = false
        this.watchCallback = null
        console.log('✅ Caption monitoring stopped')
      }
      
      return result
    } catch (error) {
      console.error('Failed to stop caption monitoring:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 停止所有进程和连接
   */
  stopAllProcesses() {
    console.log('Stopping Live Caption monitoring and disconnecting...')
    if (this.isWatchingCaptions) {
      this.stopWatchCaption()
    }
    this.disconnect()
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      this.client.disconnect()
      this.client = null
    }
    this.isConnected = false
    this.isWatchingCaptions = false
    this.watchCallback = null
  }

  /**
   * 退出时清理
   */
  exit() {
    this.stopAllProcesses()
    this.stopLiveCaption()
  }

  /**
   * Get controller tool path
   * @returns {string|null} Controller tool path, returns null if current platform is not supported
   */
  getControllerPath() {
    if (process.platform === 'win32') {
      // Windows version
      return process.env.PROD
        ? path.join(__statics, '../../lib/win_offline_asr/win_offline_asr.exe')
        : path.join(app.getAppPath(), '../../lib/win_offline_asr/win_offline_asr.exe')
    } else {
      // Other platforms not supported
      console.warn('Current platform does not support Windows Live Caption control')
      return null
    }
  }

  /**
   * 获取支持信息
   */
  getSupportInfo() {
    return {
      platform: process.platform,
      supported: process.platform === 'win32',
      serverMode: true,
      multiThreaded: true
    }
  }

  /**
   * 检查是否正在监听
   */
  isWatching() {
    return this.isWatchingCaptions
  }

  /**
   * 获取活跃进程数量
   */
  getActiveProcessCount() {
    return this.isConnected ? 1 : 0
  }

  /**
   * 获取活跃进程ID
   */
  getActiveProcessIds() {
    return this.isConnected ? ['server-mode'] : []
  }

  /**
   * Setup IPC listeners
   * Call this method in electron-main.js to register all related IPC handlers
   */
  setupIpcHandlers() {
    // Exit Live Caption
    ipcMain.handle('winLiveCaption-exit', async () => {
      this.exit()
      return { success: true, message: 'Exited successfully' }
    })

    // Start Live Caption
    ipcMain.handle('winLiveCaption-start', async () => {
      try {
        console.log('Received start Live Caption request')
        const result = await this.startLiveCaption()
        console.log('Start Live Caption result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to start Live Caption:', error)
        return { success: false, error: error.message }
      }
    })

    // Stop Live Caption
    ipcMain.handle('winLiveCaption-stop', async () => {
      try {
        console.log('Received stop Live Caption request')
        const result = await this.stopLiveCaption()
        console.log('Stop Live Caption result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to stop Live Caption:', error)
        return { success: false, error: error.message }
      }
    })

    // Hide Live Caption window
    ipcMain.handle('winLiveCaption-hide', async () => {
      try {
        console.log('Received hide Live Caption window request')
        const result = await this.hideLiveCaption()
        console.log('Hide Live Caption window result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to hide Live Caption window:', error)
        return { success: false, error: error.message }
      }
    })

    // Show Live Caption window
    ipcMain.handle('winLiveCaption-show', async () => {
      try {
        console.log('Received show Live Caption window request')
        const result = await this.showLiveCaption()
        console.log('Show Live Caption window result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to show Live Caption window:', error)
        return { success: false, error: error.message }
      }
    })

    // Get current caption content
    ipcMain.handle('winLiveCaption-getCurrentCaption', async () => {
      try {
        console.log('Received get current caption content request')
        const result = await this.getCurrentCaption()
        console.log('Get current caption content result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to get current caption content:', error)
        return { success: false, error: error.message }
      }
    })

    // Set recognition language
    ipcMain.handle('winLiveCaption-setLanguage', async (event, language) => {
      try {
        console.log('Received set recognition language request:', language)
        const result = await this.setLanguage(language)
        console.log('Set recognition language result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to set recognition language:', error)
        return { success: false, error: error.message }
      }
    })

    // Get available language list
    ipcMain.handle('winLiveCaption-getLanguageList', async (event, options = {}) => {
      try {
        console.log('Received get available language list request:', options)
        const result = await this.getLanguageList(options)
        if (result.languages && result.translations && Object.keys(result.translations).length > 0) {
          console.log('Get available language list result:', result)
          return { success: true, data: result }
        } else {
          return { success: false, data: result }
        }
      } catch (error) {
        console.error('Failed to get available language list:', error)
        return { success: false, error: error.message }
      }
    })

    // Enable microphone audio
    ipcMain.handle('winLiveCaption-enableMicAudio', async () => {
      try {
        console.log('Received enable microphone audio request')
        const result = await this.enableMicAudio()
        console.log('Enable microphone audio result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to enable microphone audio:', error)
        return { success: false, error: error.message }
      }
    })

    // Disable microphone audio
    ipcMain.handle('winLiveCaption-disableMicAudio', async () => {
      try {
        console.log('Received disable microphone audio request')
        const result = await this.disableMicAudio()
        console.log('Disable microphone audio result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to disable microphone audio:', error)
        return { success: false, error: error.message }
      }
    })

    // Start real-time caption monitoring
    ipcMain.handle('winLiveCaption-startWatchCaption', async (event) => {
      try {
        console.log('Received start real-time caption monitoring request')
        const result = await this.startWatchCaption((captionData) => {
          // Send caption data to renderer process
          console.log('captionData', captionData)
          event.sender.send('winLiveCaption-captionUpdate', captionData)
        })
        console.log('Start real-time caption monitoring result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to start real-time caption monitoring:', error)
        return { success: false, error: error.message }
      }
    })

    // Stop real-time caption monitoring
    ipcMain.handle('winLiveCaption-stopWatchCaption', async () => {
      try {
        console.log('Received stop real-time caption monitoring request')
        const result = await this.stopWatchCaption()
        console.log('Stop real-time caption monitoring result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to stop real-time caption monitoring:', error)
        return { success: false, error: error.message }
      }
    })

    // Get supported platform information
    ipcMain.handle('winLiveCaption-getSupportInfo', async () => {
      try {
        const supportInfo = this.getSupportInfo()
        console.log('Windows Live Caption support info:', supportInfo)
        return { success: true, data: supportInfo }
      } catch (error) {
        console.error('Failed to get Windows Live Caption support info:', error)
        return { success: false, error: error.message }
      }
    })

    // Check if caption monitoring is active
    ipcMain.handle('winLiveCaption-isWatching', async () => {
      try {
        const isWatching = this.isWatching()
        console.log('Windows Live Caption monitoring status:', isWatching)
        return { success: true, data: { isWatching } }
      } catch (error) {
        console.error('Failed to check Windows Live Caption monitoring status:', error)
        return { success: false, error: error.message }
      }
    })

    // Get active process information
    ipcMain.handle('winLiveCaption-getProcessInfo', async () => {
      try {
        const processInfo = {
          activeProcessCount: this.getActiveProcessCount(),
          activeProcessIds: this.getActiveProcessIds(),
          isWatching: this.isWatching(),
          isConnected: this.isConnected,
          serverMode: true
        }
        console.log('Windows Live Caption process info:', processInfo)
        return { success: true, data: processInfo }
      } catch (error) {
        console.error('Failed to get Windows Live Caption process info:', error)
        return { success: false, error: error.message }
      }
    })

    // Check if Live Caption window is visible
    ipcMain.handle('winLiveCaption-isVisible', async () => {
      try {
        console.log('Received check Live Caption visibility request')
        const result = await this.isLiveCaptionVisible()
        console.log('Live Caption visibility check result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to check Live Caption visibility:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ Windows Live Caption IPC listeners setup completed (Server Mode)')
  }
}
