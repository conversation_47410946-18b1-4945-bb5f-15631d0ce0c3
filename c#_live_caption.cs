using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows.Automation;
using System.Windows.Automation.Text;
using System.Threading;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text;
using System.IO;
using System.Text.RegularExpressions;

class Program
{
    // Windows API for sending key inputs and window management
    [DllImport("user32.dll")]
    static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

    [DllImport("user32.dll")]
    static extern bool SetCursorPos(int X, int Y);

    [DllImport("user32.dll")]
    static extern IntPtr GetWindowLongPtr(IntPtr hWnd, int nIndex);

    [DllImport("user32.dll")]
    static extern IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

    [DllImport("user32.dll")]
    static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    static extern bool IsWindowVisible(IntPtr hWnd);

    [DllImport("user32.dll")]
    static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

    [DllImport("user32.dll")]
    static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint dwData, UIntPtr dwExtraInfo);

    const uint MOUSEEVENTF_LEFTDOWN = 0x0002;
    const uint MOUSEEVENTF_LEFTUP = 0x0004;

    [StructLayout(LayoutKind.Sequential)]
    public struct RECT
    {
        public int Left;
        public int Top;
        public int Right;
        public int Bottom;
    }

    const int KEYEVENTF_KEYUP = 0x0002;
    const byte VK_CONTROL = 0x11;
    const byte VK_END = 0x23;
    const byte VK_PRIOR = 0x21; // Page Up
    const byte VK_UP = 0x26;
    const byte VK_RETURN = 0x0D;
    const byte VK_ESCAPE = 0x1B;

    // Window constants
    const int GWL_EXSTYLE = -20;
    const int WS_EX_TOOLWINDOW = 0x00000080;
    const int SW_MINIMIZE = 6;
    const int SW_RESTORE = 9;

    // Global variables
    static AutomationElement window = null;
    static bool isServerMode = false;
    static Thread watchThread = null;
    static CancellationTokenSource watchCancellationTokenSource = null;

    // ========== 辅助函数 ==========
    static string GetArgumentValue(string[] args, string argName)
    {
        for (int i = 0; i < args.Length - 1; i++)
        {
            if (args[i] == argName)
            {
                return args[i + 1];
            }
        }
        return null;
    }
    static void Main(string[] args)
    {
        // 设置控制台输出编码为UTF-8
        Console.OutputEncoding = Encoding.UTF8;
        Console.InputEncoding = Encoding.UTF8;

        if (args.Length == 0)
        {
            Console.WriteLine("用法: LiveCaptionsTool.exe [server|status|start|stop|list] [路径]");
            return;
        }

        string command = args[0].ToLower();

        // 检查是否为server模式
        if (command == "server")
        {
            isServerMode = true;
            RunServerMode();
            return;
        }

        // 非server模式，执行单个命令
        ExecuteCommand(command, args);
    }

    static void ExecuteCommand(string command, string[] args)
    {
        switch (command)
        {
            case "status":
                GetWindowStatus();
                break;
            case "start":
                string path = args.Length > 1 ? args[1] : "LiveCaptions.exe";
                StartLiveCaptions(path);
                break;
            case "stop":
                StopLiveCaptions();
                break;
            case "hide":
                HideWindow();
                break;
            case "show":
                ShowWindow();
                break;
            case "get_caption":
                bool watch = args.Contains("--watch");
                GetCaption(watch);
                break;
            case "set_language":
                string lang = GetArgumentValue(args, "--lang");
                if (string.IsNullOrEmpty(lang))
                {
                    Console.WriteLine(JsonSerializer.Serialize(new { success = false, error = "缺少--lang参数" }));
                }
                else
                {
                    SetLanguage(lang);
                }
                break;
            case "debug_elements":
                DebugElements();
                break;
            case "list":
                bool fast = args.Contains("--fast");
                bool noTranslate = args.Contains("--no-translate");
                string apiKey = GetArgumentValue(args, "--api-key");
                ListLanguages(fast, noTranslate, apiKey);
                break;
            case "enable_mic_audio":
                EnableMicAudio();
                break;
            case "disable_mic_audio":
                DisableMicAudio();
                break;
            case "get_ready_text":
                GetReadyText();
                break;
            case "start_watch":
                StartWatchCaption();
                break;
            case "stop_watch":
                StopWatchCaption();
                break;
            default:
                Console.WriteLine(JsonSerializer.Serialize(new { success = false, error = "未知命令" }));
                break;
        }
    }

    static void RunServerMode()
    {
        Console.WriteLine(JsonSerializer.Serialize(new { status = "server_started" }));
        Console.Out.Flush();

        string line;
        while ((line = Console.ReadLine()) != null)
        {
            try
            {
                var commandData = JsonSerializer.Deserialize<JsonElement>(line);
                string command = commandData.GetProperty("command").GetString();

                // 解析参数
                var args = new List<string> { command };
                if (commandData.TryGetProperty("args", out var argsElement))
                {
                    foreach (var arg in argsElement.EnumerateArray())
                    {
                        args.Add(arg.GetString());
                    }
                }

                ExecuteCommand(command, args.ToArray());
                Console.Out.Flush();
            }
            catch (Exception ex)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false, error = ex.Message }));
                Console.Out.Flush();
            }
        }
    }

    // ========== 窗口连接功能 ==========
    static bool ConnectWindow()
    {
        try
        {
            var processes = Process.GetProcessesByName("LiveCaptions");
            if (processes.Length == 0) return false;

            foreach (var proc in processes)
            {
                try
                {
                    if (proc.MainWindowHandle != IntPtr.Zero)
                    {
                        window = AutomationElement.FromHandle(proc.MainWindowHandle);
                        if (window != null)
                        {
                            return true;
                        }
                    }
                }
                catch { continue; }
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    static void StartLiveCaptions(string path)
    {
        try
        {
            string readyText = ""; // 在函数开始就声明变量

            // 检查是否已经在运行
            var processes = Process.GetProcessesByName("LiveCaptions");
            if (processes.Length > 0)
            {
                // 已经在运行，尝试获取ready_text
                try
                {
                    if (ConnectWindow())
                    {
                        var readyTextElement = FindElementByAutomationId(window, "ReadyToCaptionTextBlock");
                        if (readyTextElement != null)
                        {
                            readyText = readyTextElement.Current.Name;
                            // 如果Name为空，尝试使用TextPattern
                            if (string.IsNullOrEmpty(readyText))
                            {
                                var textPattern = readyTextElement.GetCurrentPattern(TextPattern.Pattern) as TextPattern;
                                if (textPattern != null)
                                {
                                    readyText = textPattern.DocumentRange.GetText(-1);
                                }
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // 获取ready_text失败，保持空字符串
                }

                Console.WriteLine(JsonSerializer.Serialize(new { success = true, ready_text = readyText }));
                return;
            }

            Process.Start(path);

            // 循环检查进程是否启动，最多等待5秒
            bool success = false;
            for (int i = 0; i < 10; i++) // 50次 * 100ms = 5秒最大等待时间
            {
                Thread.Sleep(100);
                processes = Process.GetProcessesByName("LiveCaptions");
                if (processes.Length > 0)
                {
                    success = true;
                    break; // 进程启动成功，立即退出循环
                }
            }
            // 如果启动成功，等待窗口可用并立即隐藏
            if (success)
            {
                // 等待窗口完全加载，最多等待3秒
                for (int i = 0; i < 30; i++)
                {
                    Thread.Sleep(100);
                    if (ConnectWindow())
                    {
                        // 连接成功后获取ready_text
                        try
                        {
                            var readyTextElement = FindElementByAutomationId(window, "ReadyToCaptionTextBlock");
                            if (readyTextElement != null)
                            {
                                readyText = readyTextElement.Current.Name;
                                // 如果Name为空，尝试使用TextPattern
                                if (string.IsNullOrEmpty(readyText))
                                {
                                    var textPattern = readyTextElement.GetCurrentPattern(TextPattern.Pattern) as TextPattern;
                                    if (textPattern != null)
                                    {
                                        readyText = textPattern.DocumentRange.GetText(-1);
                                    }
                                }
                            }
                        }
                        catch (Exception)
                        {
                            // 获取ready_text失败，保持空字符串
                        }

                        // 连接成功后立即隐藏
                        HideWindow(false);
                        break;
                    }
                }
            }
            Console.WriteLine(JsonSerializer.Serialize(new { success, ready_text = readyText }));
        }
        catch (Exception)
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false, ready_text = "" }));
        }
    }

    // ========== 窗口控制功能 ==========
    static void HideWindow(bool console = true)
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                if (console)
                {
                    Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                }
                return;
            }

            IntPtr hWnd = new IntPtr(window.Current.NativeWindowHandle);
            IntPtr exStyle = GetWindowLongPtr(hWnd, GWL_EXSTYLE);
            ShowWindow(hWnd, SW_MINIMIZE);
            IntPtr newExStyle = new IntPtr(exStyle.ToInt64() | WS_EX_TOOLWINDOW);
            SetWindowLongPtr(hWnd, GWL_EXSTYLE, newExStyle);
            if (console)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = true }));
            }
        }
        catch
        {
            if (console)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
            }
        }
    }

    static void ShowWindow()
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                return;
            }

            IntPtr hWnd = new IntPtr(window.Current.NativeWindowHandle);
            ShowWindow(hWnd, SW_RESTORE);
            Console.WriteLine(JsonSerializer.Serialize(new { success = true }));
        }
        catch
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
        }
    }

    static void GetWindowStatus()
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                Console.WriteLine(JsonSerializer.Serialize(new { visible = false }));
                return;
            }

            IntPtr hWnd = new IntPtr(window.Current.NativeWindowHandle);

            // 检查窗口是否可见
            if (!IsWindowVisible(hWnd))
            {
                Console.WriteLine(JsonSerializer.Serialize(new { visible = false }));
                return;
            }

            // 检查窗口边界矩形
            if (GetWindowRect(hWnd, out RECT rect))
            {
                if (rect.Right - rect.Left <= 0 || rect.Bottom - rect.Top <= 0)
                {
                    Console.WriteLine(JsonSerializer.Serialize(new { visible = false }));
                    return;
                }
            }

            Console.WriteLine(JsonSerializer.Serialize(new { visible = true }));
        }
        catch
        {
            Console.WriteLine(JsonSerializer.Serialize(new { visible = false }));
        }
    }

    // ========== 字幕获取功能 ==========
    static void GetCaption(bool watch)
    {
        if (watch)
        {
            if (isServerMode)
            {
                StartWatchCaption();
            }
            else
            {
                WatchCaptionText();
            }
        }
        else
        {
            string text = GetCaptionText();
            var result = new { caption = text };
            var options = new JsonSerializerOptions
            {
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            Console.WriteLine(JsonSerializer.Serialize(result, options));
        }
    }

    static void StartWatchCaption()
    {
        if (watchThread != null && watchThread.IsAlive)
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false, error = "watch已经在运行" }));
            return;
        }

        watchCancellationTokenSource = new CancellationTokenSource();
        watchThread = new Thread(() => WatchCaptionTextThreaded(watchCancellationTokenSource.Token))
        {
            IsBackground = true
        };
        watchThread.Start();

        Console.WriteLine(JsonSerializer.Serialize(new { success = true, message = "watch已启动" }));
    }

    static void StopWatchCaption()
    {
        if (watchCancellationTokenSource != null)
        {
            watchCancellationTokenSource.Cancel();
        }

        if (watchThread != null && watchThread.IsAlive)
        {
            watchThread.Join(1000); // 等待最多1秒
        }

        Console.WriteLine(JsonSerializer.Serialize(new { success = true, message = "watch已停止" }));
    }

    static void WatchCaptionTextThreaded(CancellationToken cancellationToken)
    {
        string lastText = null;
        var options = new JsonSerializerOptions
        {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                string text = GetCaptionText();
                if (text != lastText)
                {
                    var result = new { type = "caption_update", caption = text };
                    Console.WriteLine(JsonSerializer.Serialize(result, options));
                    Console.Out.Flush();
                    lastText = text;
                }
                Thread.Sleep(25); // 0.1秒间隔
            }
        }
        catch (Exception)
        {
            // 静默处理异常，可能是取消操作
        }
    }

    static void DebugElements()
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                Console.WriteLine("无法连接到LiveCaptions窗口");
                return;
            }

            Console.WriteLine("=== LiveCaptions窗口元素调试信息 ===");
            Console.WriteLine($"窗口名称: {window.Current.Name}");
            Console.WriteLine($"窗口类名: {window.Current.ClassName}");
            Console.WriteLine($"窗口AutomationId: {window.Current.AutomationId}");

            // 递归遍历所有元素
            DebugElementRecursive(window, 0, 3); // 最多3层深度
        }
        catch (Exception ex)
        {
            Console.WriteLine($"调试时出现异常: {ex.Message}");
        }
    }

    static void DebugElementRecursive(AutomationElement element, int depth, int maxDepth)
    {
        if (depth > maxDepth) return;

        var walker = TreeWalker.ControlViewWalker;
        var child = walker.GetFirstChild(element);
        int count = 0;

        while (child != null && count < 10) // 每层最多10个元素
        {
            string indent = new string(' ', depth * 2);
            Console.WriteLine($"{indent}元素 {count + 1} (深度{depth}):");
            Console.WriteLine($"{indent}  名称: '{child.Current.Name}'");
            Console.WriteLine($"{indent}  类名: {child.Current.ClassName}");
            Console.WriteLine($"{indent}  AutomationId: '{child.Current.AutomationId}'");
            Console.WriteLine($"{indent}  控件类型: {child.Current.ControlType.LocalizedControlType}");
            Console.WriteLine($"{indent}  边界: {child.Current.BoundingRectangle}");

            // 尝试获取文本内容
            try
            {
                var textPattern = child.GetCurrentPattern(TextPattern.Pattern) as TextPattern;
                if (textPattern != null)
                {
                    string text = textPattern.DocumentRange.GetText(-1);
                    if (!string.IsNullOrEmpty(text))
                    {
                        Console.WriteLine($"{indent}  文本内容: '{text.Substring(0, Math.Min(text.Length, 100))}'");
                    }
                }
            }
            catch { }

            Console.WriteLine();

            // 递归查看子元素
            DebugElementRecursive(child, depth + 1, maxDepth);

            child = walker.GetNextSibling(child);
            count++;
        }
    }

    static string GetCaptionText()
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                return null;
            }

            // 方法1: 直接查找CaptionsTextBlock元素
            var captionsTextBlock = FindElementByAutomationId(window, "CaptionsTextBlock");
            if (captionsTextBlock != null)
            {
                // 尝试获取文本内容
                var textPattern = captionsTextBlock.GetCurrentPattern(TextPattern.Pattern) as TextPattern;
                if (textPattern != null)
                {
                    string text = textPattern.DocumentRange.GetText(-1);
                    if (!string.IsNullOrEmpty(text))
                    {
                        return text.Trim();
                    }
                }

                // 如果TextPattern不可用，尝试获取Name属性
                string name = captionsTextBlock.Current.Name;
                if (!string.IsNullOrEmpty(name))
                {
                    return name.Trim();
                }
            }

            // 方法2: 通过CaptionsScrollViewer查找
            var captionsScrollViewer = FindElementByAutomationId(window, "CaptionsScrollViewer");
            if (captionsScrollViewer != null)
            {
                // 先尝试获取ScrollViewer的Name属性
                string scrollViewerName = captionsScrollViewer.Current.Name;
                if (!string.IsNullOrEmpty(scrollViewerName))
                {
                    return scrollViewerName.Trim();
                }

                // 查找其中的TextBlock子元素
                var textBlockCondition = new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.Text);
                var textBlock = captionsScrollViewer.FindFirst(TreeScope.Children, textBlockCondition);
                if (textBlock != null)
                {
                    string textBlockName = textBlock.Current.Name;
                    if (!string.IsNullOrEmpty(textBlockName))
                    {
                        return textBlockName.Trim();
                    }
                }
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    static void WatchCaptionText()
    {
        string lastText = null;
        var options = new JsonSerializerOptions
        {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        try
        {
            while (true)
            {
                string text = GetCaptionText();
                if (text != lastText)
                {
                    var result = new { caption = text };
                    Console.WriteLine(JsonSerializer.Serialize(result, options));
                    Console.Out.Flush();
                    lastText = text;
                }
                Thread.Sleep(500); // 0.5秒间隔
            }
        }
        catch (Exception)
        {
            // 静默处理异常，可能是Ctrl+C中断
        }
    }

    // ========== 语言设置功能 ==========
    static void SetLanguage(string languagePrefix)
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                return;
            }

            // 首先确保所有菜单都关闭
            SendEscapeKey();
            Thread.Sleep(50); // 减少延迟

            // 点击设置按钮
            var settingsButton = FindElementByAutomationId(window, "SettingsButton");
            if (settingsButton == null)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                return;
            }

            InvokeElement(settingsButton);
            Thread.Sleep(500); // 等待菜单出现

            // 查找更改语言菜单项
            AutomationElement changeLanguageItem = null;

            // 方法1: 通过弹出窗口查找
            try
            {
                var popupCondition = new PropertyCondition(AutomationElement.ClassNameProperty, "Xaml_WindowedPopupClass");
                var popup = window.FindFirst(TreeScope.Children, popupCondition);
                if (popup != null)
                {
                    changeLanguageItem = FindElementByAutomationId(popup, "ChangeLanguageMenuFlyoutItem");
                }
            }
            catch { }

            // 方法2: 直接在窗口中查找
            if (changeLanguageItem == null)
            {
                changeLanguageItem = FindElementByAutomationId(window, "ChangeLanguageMenuFlyoutItem");
            }

            if (changeLanguageItem == null)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                return;
            }

            InvokeElement(changeLanguageItem);
            Thread.Sleep(500); // 等待语言列表出现

            // 查找语言下拉列表
            var languageComboBox = FindElementByAutomationId(window, "SpeechModelDropDown");
            if (languageComboBox == null)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
                return;
            }

            // 展开下拉列表
            ExpandComboBox(languageComboBox);
            Thread.Sleep(300); // 等待列表展开

            // 查找匹配的语言
            bool found = FindAndSelectLanguage(languageComboBox, languagePrefix);

            if (found)
            {
                // 点击继续按钮
                var continueButton = FindElementByAutomationId(window, "ContinueButton");
                if (continueButton != null)
                {
                    InvokeElement(continueButton);
                }
                else
                {
                    // 备用方案：发送Enter键
                    SendEnterKey();
                }
                Console.WriteLine(JsonSerializer.Serialize(new { success = true }));
            }
            else
            {
                // 未找到语言，关闭设置
                SendEscapeKey();
                Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
            }
        }
        catch
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
        }
    }

    static bool FindAndSelectLanguage(AutomationElement comboBox, string languagePrefix)
    {
        try
        {
            // 移动到列表顶部
            SendHomeKey();
            Thread.Sleep(100);

            var seenItems = new HashSet<string>();
            int maxAttempts = 50;

            for (int attempt = 0; attempt < maxAttempts; attempt++)
            {
                // 获取当前可见的列表项
                var itemCondition = new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ListItem);
                var items = comboBox.FindAll(TreeScope.Descendants, itemCondition);

                var currentItems = new List<string>();
                foreach (AutomationElement item in items)
                {
                    string itemText = item.Current.Name?.Trim();
                    if (!string.IsNullOrEmpty(itemText))
                    {
                        currentItems.Add(itemText);

                        // 检查是否匹配语言前缀
                        if (itemText.StartsWith(languagePrefix, StringComparison.OrdinalIgnoreCase))
                        {
                            // 找到匹配的语言，选择它
                            var selectionPattern = item.GetCurrentPattern(SelectionItemPattern.Pattern) as SelectionItemPattern;
                            if (selectionPattern != null)
                            {
                                selectionPattern.Select();
                                Thread.Sleep(200);
                                return true;
                            }
                        }
                    }
                }

                // 检查是否有新项目
                var currentTexts = new HashSet<string>(currentItems);
                if (currentTexts.IsSubsetOf(seenItems))
                {
                    // 没有新项目，尝试向下滚动
                    SendPageDownKey();
                    Thread.Sleep(30); // 减少延迟

                    // 再次检查是否有新项目
                    items = comboBox.FindAll(TreeScope.Descendants, itemCondition);
                    var newTexts = new HashSet<string>();
                    foreach (AutomationElement item in items)
                    {
                        string itemText = item.Current.Name?.Trim();
                        if (!string.IsNullOrEmpty(itemText))
                        {
                            newTexts.Add(itemText);
                        }
                    }

                    if (newTexts.IsSubsetOf(seenItems))
                    {
                        break; // 已经到底了
                    }
                }

                seenItems.UnionWith(currentTexts);

                // 向下移动几个项目
                SendDownArrowKeys(3);
                Thread.Sleep(30); // 减少延迟
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    static void StopLiveCaptions()
    {
        try
        {
            var processes = Process.GetProcessesByName("LiveCaptions");
            if (processes.Length > 0)
            {
                bool allKilled = true;
                foreach (var proc in processes)
                {
                    try
                    {
                        proc.Kill();
                    }
                    catch
                    {
                        allKilled = false;
                    }
                }
                Console.WriteLine(JsonSerializer.Serialize(new { success = allKilled }));
            }
            else
            {
                // 没有运行的进程，也算成功
                Console.WriteLine(JsonSerializer.Serialize(new { success = true }));
            }
        }
        catch
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false }));
        }
    }

    static void ListLanguages(bool fast = false, bool noTranslate = false, string apiKey = null)
    {
        try
        {
            // 获取语言列表
            List<string> languages;
            if (fast)
            {
                languages = GetLanguageListFast();
            }
            else
            {
                languages = GetLanguageList();
            }

            var options = new JsonSerializerOptions
            {
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            if (noTranslate)
            {
                // 不翻译，只返回原始语言列表
                var result = new { languages };
                Console.WriteLine(JsonSerializer.Serialize(result, options));
            }
            else
            {
                // 默认使用翻译功能
                var translations = TranslateLanguagesToEnglish(languages, apiKey);
                var result = new { languages, translations };
                Console.WriteLine(JsonSerializer.Serialize(result, options));
            }
        }
        catch (Exception)
        {
            // 出错时返回空的语言列表
            var result = new { languages = new List<string>() };
            var options = new JsonSerializerOptions
            {
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            Console.WriteLine(JsonSerializer.Serialize(result, options));
        }
    }

    static List<string> GetLanguageList()
    {
        var languages = new List<string>();
        var seen = new HashSet<string>();

        try
        {
            if (window == null && !ConnectWindow())
            {
                return languages;
            }

            // 首先确保所有菜单都关闭
            SendEscapeKey();
            Thread.Sleep(50); // 减少延迟

            // 点击设置按钮
            var settingsButton = FindElementByAutomationId(window, "SettingsButton");
            if (settingsButton == null) return languages;

            InvokeElement(settingsButton);
            Thread.Sleep(500);

            // 查找更改语言菜单项
            AutomationElement changeLanguageItem = null;
            try
            {
                var popupCondition = new PropertyCondition(AutomationElement.ClassNameProperty, "Xaml_WindowedPopupClass");
                var popup = window.FindFirst(TreeScope.Children, popupCondition);
                if (popup != null)
                {
                    changeLanguageItem = FindElementByAutomationId(popup, "ChangeLanguageMenuFlyoutItem");
                }
            }
            catch { }

            if (changeLanguageItem == null)
            {
                changeLanguageItem = FindElementByAutomationId(window, "ChangeLanguageMenuFlyoutItem");
            }

            if (changeLanguageItem == null) return languages;

            InvokeElement(changeLanguageItem);
            Thread.Sleep(500);

            // 查找语言下拉列表
            var languageComboBox = FindElementByAutomationId(window, "SpeechModelDropDown");
            if (languageComboBox == null) return languages;

            // 展开下拉列表
            ExpandComboBox(languageComboBox);
            Thread.Sleep(300);

            // 使用标准滚动方法获取所有语言
            languages = GetAllLanguagesFromComboBox(languageComboBox, false); // false = 标准方法

            // 清理UI
            CleanupLanguageUI(languageComboBox);
        }
        catch (Exception)
        {
            // 静默处理异常
        }

        return languages;
    }

    static List<string> GetLanguageListFast()
    {
        var languages = new List<string>();
        var seen = new HashSet<string>();

        try
        {
            // 查找LiveCaptions窗口
            AutomationElement window = FindLiveCaptionsWindow();
            if (window == null)
            {
                return languages;
            }

            // 点击设置按钮
            var settingsButton = FindElementByAutomationId(window, "SettingsButton");
            if (settingsButton == null)
            {
                return languages;
            }

            InvokeElement(settingsButton);
            Thread.Sleep(500); // 等待菜单出现

            // 查找并点击"更改语言"菜单项
            var changeLanguageItem = FindElementByAutomationId(window, "ChangeLanguageMenuFlyoutItem");
            if (changeLanguageItem == null)
            {
                // 尝试通过弹出窗口查找
                var popupPane = FindElementByClassName(window, "Xaml_WindowedPopupClass");
                if (popupPane != null)
                {
                    changeLanguageItem = FindElementByAutomationId(popupPane, "ChangeLanguageMenuFlyoutItem");
                }
            }

            if (changeLanguageItem == null)
            {
                return languages;
            }

            InvokeElement(changeLanguageItem);
            Thread.Sleep(500); // 等待语言列表出现

            // 查找语言下拉列表
            var languageComboBox = FindElementByAutomationId(window, "SpeechModelDropDown");
            if (languageComboBox == null)
            {
                return languages;
            }

            // 展开下拉列表
            ExpandComboBox(languageComboBox);
            Thread.Sleep(300);

            // 设置焦点
            languageComboBox.SetFocus();

            // 快速滚动到底部，然后向上扫描
            SendKey(VK_CONTROL, VK_END); // Ctrl+End 跳到底部
            Thread.Sleep(100);

            // 快速向上扫描，收集所有语言项
            for (int i = 0; i < 20; i++) // 最多20次大幅向上移动
            {
                var items = GetListItems(languageComboBox);
                var newItems = items.Where(item => !string.IsNullOrWhiteSpace(item)).ToList();

                foreach (var text in newItems)
                {
                    if (!string.IsNullOrEmpty(text) && !seen.Contains(text))
                    {
                        seen.Add(text);
                        languages.Add(text);
                    }
                }

                // 大幅向上移动
                SendSingleKey(VK_PRIOR); // Page Up
                Thread.Sleep(30); // 减少延迟

                // 检查是否到顶部
                try
                {
                    SendSingleKey(VK_UP);
                    // Thread.Sleep(20); // 去掉短延迟
                    var currentItems = GetListItems(languageComboBox);
                    if (currentItems.SequenceEqual(newItems)) // 没有变化，说明到顶了
                    {
                        break;
                    }
                }
                catch
                {
                    break;
                }
            }

            // 关闭下拉列表和设置菜单
            CleanupUI(languageComboBox, window);

        }
        catch (Exception)
        {
            // 静默处理异常，返回空列表
        }

        return languages;
    }

    static List<string> GetAllLanguagesFromComboBox(AutomationElement comboBox, bool useFastMethod)
    {
        var languages = new List<string>();
        var seen = new HashSet<string>();

        try
        {
            // 设置焦点
            comboBox.SetFocus();

            if (useFastMethod)
            {
                // 快速方法：跳到底部然后向上扫描
                SendKey(VK_CONTROL, VK_END); // Ctrl+End 跳到底部
                Thread.Sleep(100);

                // 快速向上扫描，收集所有语言项
                for (int i = 0; i < 20; i++) // 最多20次大幅向上移动
                {
                    var items = GetListItems(comboBox);
                    var newItems = items.Where(item => !string.IsNullOrWhiteSpace(item)).ToList();

                    foreach (var text in newItems)
                    {
                        if (!string.IsNullOrEmpty(text) && !seen.Contains(text))
                        {
                            seen.Add(text);
                            languages.Add(text);
                        }
                    }

                    // 如果没有新项目，停止
                    if (newItems.Count == 0 || newItems.All(seen.Contains))
                    {
                        break;
                    }

                    // 大幅向上移动
                    for (int j = 0; j < 10; j++)
                    {
                        SendSingleKey(0x26); // VK_UP
                        // Thread.Sleep(5); // 去掉短延迟
                    }
                }
            }
            else
            {
                // 标准方法：从顶部开始逐步向下滚动
                SendSingleKey(0x24); // VK_HOME 跳到顶部
                Thread.Sleep(100);

                int maxAttempts = 50;
                for (int attempt = 0; attempt < maxAttempts; attempt++)
                {
                    var items = GetListItems(comboBox);
                    var currentItems = items.Where(item => !string.IsNullOrWhiteSpace(item)).ToList();

                    bool hasNewItems = false;
                    foreach (var text in currentItems)
                    {
                        if (!string.IsNullOrEmpty(text) && !seen.Contains(text))
                        {
                            seen.Add(text);
                            languages.Add(text);
                            hasNewItems = true;
                        }
                    }

                    // 如果没有新项目，尝试向下滚动
                    if (!hasNewItems)
                    {
                        // 尝试Page Down
                        SendSingleKey(VK_PRIOR); // Page Down
                        Thread.Sleep(30); // 减少延迟

                        // 再次检查是否有新项目
                        var newItems = GetListItems(comboBox).Where(item => !string.IsNullOrWhiteSpace(item)).ToList();
                        if (newItems.All(seen.Contains))
                        {
                            break; // 已经到底了
                        }
                    }

                    // 向下移动几个项目
                    for (int j = 0; j < 3; j++)
                    {
                        SendSingleKey(0x28); // VK_DOWN
                        // Thread.Sleep(10); // 去掉短延迟
                    }
                }
            }
        }
        catch (Exception)
        {
            // 静默处理异常
        }

        return languages.Distinct().ToList();
    }

    static void CleanupLanguageUI(AutomationElement languageComboBox)
    {
        try
        {
            // 关闭下拉列表
            CollapseComboBox(languageComboBox);
            Thread.Sleep(50); // 减少延迟

            // 按ESC键关闭菜单
            SendEscapeKey();
            Thread.Sleep(50); // 减少延迟
        }
        catch
        {
            // 静默处理异常
        }
    }

    static void EnableMicAudio()
    {
        bool success = SetMicAudioCheckbox(true);
        Console.WriteLine(JsonSerializer.Serialize(new { success }));
    }

    static void DisableMicAudio()
    {
        bool success = SetMicAudioCheckbox(false);
        Console.WriteLine(JsonSerializer.Serialize(new { success }));
    }

    static void GetReadyText()
    {
        try
        {
            if (window == null && !ConnectWindow())
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false, text = "" }));
                return;
            }

            // 查找ReadyToCaptionTextBlock元素
            var readyTextElement = FindElementByAutomationId(window, "ReadyToCaptionTextBlock");
            if (readyTextElement == null)
            {
                Console.WriteLine(JsonSerializer.Serialize(new { success = false, text = "" }));
                return;
            }

            // 获取文本内容
            string text = "";
            try
            {
                // 尝试获取Name属性
                text = readyTextElement.Current.Name;

                // 如果Name为空，尝试使用TextPattern
                if (string.IsNullOrEmpty(text))
                {
                    var textPattern = readyTextElement.GetCurrentPattern(TextPattern.Pattern) as TextPattern;
                    if (textPattern != null)
                    {
                        text = textPattern.DocumentRange.GetText(-1);
                    }
                }
            }
            catch (Exception)
            {
                // 如果获取失败，返回空文本
                text = "";
            }

            Console.WriteLine(JsonSerializer.Serialize(new { success = true, text = text }));
        }
        catch (Exception)
        {
            Console.WriteLine(JsonSerializer.Serialize(new { success = false, text = "" }));
        }
    }

    static void ClickCenterOfElement(AutomationElement element)
    {
        var rect = element.Current.BoundingRectangle;
        int x = (int)((rect.Left + rect.Right) / 2);
        int y = (int)((rect.Top + rect.Bottom) / 2);

        SetCursorPos(x, y);
        Thread.Sleep(50); // 减少延迟
        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, UIntPtr.Zero);
        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, UIntPtr.Zero);
    }


    static bool SetMicAudioCheckbox(bool enable)
    {
        if (window == null && !ConnectWindow())
        {
            // Console.Error.WriteLine("无法连接到LiveCaptions窗口");
            return false;
        }

        // 点击设置按钮
        var settingsButton = FindElementByAutomationId(window, "SettingsButton");
        if (settingsButton == null)
        {
            // Console.Error.WriteLine("找不到设置按钮");
            return false;
        }

        InvokeElement(settingsButton);
        Thread.Sleep(500);

        // 查找弹出窗口面板
        AutomationElement popupPane = null;
        try
        {
            var popupCondition = new PropertyCondition(AutomationElement.ClassNameProperty, "Xaml_WindowedPopupClass");
            popupPane = window.FindFirst(TreeScope.Children, popupCondition);
            if (popupPane == null)
            {
                // Console.Error.WriteLine("找不到弹出窗口面板");
                return false;
            }
            Thread.Sleep(100); // 减少延迟
        }
        catch (Exception ex)
        {
            // Console.Error.WriteLine($"查找弹出窗口时出错: {ex.Message}");
            return false;
        }

        // 查找设置菜单
        var settingsMenu = FindElementByAutomationId(popupPane, "SettingsMenuFlyout");
        if (settingsMenu == null)
        {
            // Console.Error.WriteLine("找不到设置菜单");
            SendEscapeKey();
            return false;
        }

        // 查找首选项按钮
        var preferencesButton = FindElementByAutomationId(settingsMenu, "PreferencesButton");
        if (preferencesButton == null)
        {
            // Console.Error.WriteLine("找不到首选项按钮");
            SendEscapeKey();
            return false;
        }

        // Console.Error.WriteLine("找到首选项按钮，点击首选项...");

        // 使用鼠标点击首选项（因为InvokeElement可能对子菜单项不起作用）
        try
        {
            ClickCenterOfElement(preferencesButton);
            // Console.Error.WriteLine("使用鼠标点击首选项");
            Thread.Sleep(500);
        }
        catch (Exception ex)
        {
            // Console.Error.WriteLine($"鼠标点击首选项失败: {ex.Message}");
            // 尝试使用InvokeElement作为备选方案
            try
            {
                InvokeElement(preferencesButton);
                // Console.Error.WriteLine("使用InvokeElement点击首选项");
                Thread.Sleep(500);
            }
            catch (Exception ex2)
            {
                // Console.Error.WriteLine($"InvokeElement点击首选项也失败: {ex2.Message}");
                SendEscapeKey();
                return false;
            }
        }

        // Console.Error.WriteLine("点击首选项后，查找麦克风选项...");
        Thread.Sleep(1000);

        // 先显示点击首选项后的完整结构
        // Console.Error.WriteLine("点击首选项后的完整UI结构:");
        // DebugElements();

        // 重新获取更新后的设置菜单
        var updatedSettingsMenu = FindElementByAutomationId(popupPane, "SettingsMenuFlyout");
        if (updatedSettingsMenu == null)
        {
            // Console.Error.WriteLine("找不到更新后的设置菜单");
            SendEscapeKey();
            return false;
        }

        // Console.Error.WriteLine("更新后的设置菜单内容:");
        // DebugPrintChildrenRecursive(updatedSettingsMenu, 0);

        // 查找麦克风菜单项
        AutomationElement microphoneMenuItem = null;

        var _popupCondition = new PropertyCondition(AutomationElement.ClassNameProperty, "Xaml_WindowedPopupClass");
        var popupPanes = window.FindAll(TreeScope.Children, _popupCondition);

        foreach (AutomationElement pane in popupPanes)
        {
            var _settingsMenu = FindElementByAutomationId(pane, "SettingsMenuFlyout");
            if (_settingsMenu == null) continue;

            DebugPrintChildrenRecursive(settingsMenu, 0); // 调试用，可注释掉

            microphoneMenuItem = FindElementByAutomationId(settingsMenu, "MicrophoneMenuFlyoutItem");
            if (microphoneMenuItem != null)
            {
                // Console.Error.WriteLine("在SettingsMenuFlyout中找到麦克风菜单项！");
                break;
            }
        }

        // 如果还没找到，在所有 popupPane 里继续找
        if (microphoneMenuItem == null)
        {
            foreach (AutomationElement pane in popupPanes)
            {
                microphoneMenuItem = FindElementByAutomationId(pane, "MicrophoneMenuFlyoutItem");
                if (microphoneMenuItem != null)
                {
                    // Console.Error.WriteLine("在整个弹出面板中找到了麦克风菜单项！");
                    break;
                }
            }
        }

        if (microphoneMenuItem == null)
        {
            // Console.Error.WriteLine("仍然找不到麦克风菜单项！");
            SendEscapeKey();
            return false;
        }


        // 检查当前选中状态
        try
        {
            var togglePattern = microphoneMenuItem.GetCurrentPattern(TogglePattern.Pattern) as TogglePattern;
            if (togglePattern != null)
            {
                var currentState = togglePattern.Current.ToggleState;
                // Console.Error.WriteLine($"当前麦克风选项状态: {currentState}");

                // 根据需要的状态决定是否点击
                bool shouldBeOn = enable; // enable参数决定是否应该开启
                bool isCurrentlyOn = (currentState == ToggleState.On);

                // Console.Error.WriteLine($"需要状态: {(shouldBeOn ? "开启" : "关闭")}, 当前状态: {(isCurrentlyOn ? "开启" : "关闭")}");

                if (shouldBeOn != isCurrentlyOn)
                {
                    // Console.Error.WriteLine("状态不匹配，点击切换...");
                    ClickCenterOfElement(microphoneMenuItem);
                    ClickCenterOfElement(microphoneMenuItem);
                    Thread.Sleep(100); // 减少延迟

                    // 验证状态是否已更改
                    var newState = togglePattern.Current.ToggleState;
                    // Console.Error.WriteLine($"点击后状态: {newState}");
                }
                else
                {
                    // Console.Error.WriteLine("状态已经正确，无需点击");
                }
            }
            else
            {
                // Console.Error.WriteLine("无法获取切换模式，直接点击");
                ClickCenterOfElement(microphoneMenuItem);
                Thread.Sleep(100); // 减少延迟
            }
        }
        catch (Exception ex)
        {
            // Console.Error.WriteLine($"处理麦克风选项时出错: {ex.Message}");
            // 如果无法检查状态，就直接点击
            ClickCenterOfElement(microphoneMenuItem);
            Thread.Sleep(100); // 减少延迟
        }

        // 关闭菜单
        SendEscapeKey();
        SendEscapeKey();
        // Console.Error.WriteLine($"麦克风音频设置完成: {(enable ? "启用" : "禁用")}");
        return true;


    }

    static void DebugPrintChildren(AutomationElement element)
    {
        try
        {
            var children = element.FindAll(TreeScope.Children, Condition.TrueCondition);
            foreach (AutomationElement child in children)
            {
                try
                {
                    var automationId = child.Current.AutomationId;
                    var name = child.Current.Name;
                    var controlType = child.Current.ControlType.LocalizedControlType;
                    // Console.Error.WriteLine($"  - AutomationId: '{automationId}', Name: '{name}', Type: {controlType}");
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            // Console.Error.WriteLine($"调试输出子元素时出错: {ex.Message}");
        }
    }

    static void DebugPrintChildrenRecursive(AutomationElement element, int depth)
    {
        try
        {
            string indent = new string(' ', depth * 2);
            var children = element.FindAll(TreeScope.Children, Condition.TrueCondition);
            foreach (AutomationElement child in children)
            {
                try
                {
                    var automationId = child.Current.AutomationId;
                    var name = child.Current.Name;
                    var controlType = child.Current.ControlType.LocalizedControlType;
                    // Console.Error.WriteLine($"{indent}- AutomationId: '{automationId}', Name: '{name}', Type: {controlType}");

                    // 递归打印子元素，但限制深度避免无限递归
                    if (depth < 3)
                    {
                        DebugPrintChildrenRecursive(child, depth + 1);
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            // Console.Error.WriteLine($"递归调试输出子元素时出错: {ex.Message}");
        }
    }

    static AutomationElement FindElementRecursive(AutomationElement parent, string automationId)
    {
        try
        {
            // 首先在直接子元素中查找
            var condition = new PropertyCondition(AutomationElement.AutomationIdProperty, automationId);
            var element = parent.FindFirst(TreeScope.Children, condition);
            if (element != null)
            {
                return element;
            }

            // 如果没找到，递归查找所有后代元素
            element = parent.FindFirst(TreeScope.Descendants, condition);
            return element;
        }
        catch
        {
            return null;
        }
    }

    static Dictionary<string, string> TranslateLanguagesToEnglish(List<string> languages, string apiKey = null)
    {
        var translations = new Dictionary<string, string>();

        if (languages == null || languages.Count == 0)
        {
            return translations;
        }

        try
        {
            // 使用同步方式调用异步方法
            var task = TranslateLanguagesToEnglishAsync(languages, apiKey);
            task.Wait();
            return task.Result;
        }
        catch (Exception)
        {
            // 如果翻译失败，返回空字典
            return translations;
        }
    }

    static async Task<Dictionary<string, string>> TranslateLanguagesToEnglishAsync(List<string> languages, string apiKey = null)
    {
        var translations = new Dictionary<string, string>();

        try
        {
            // 在构建 requestData 之前，先清理括号内容
            var cleanedLanguages = languages
                .Select(lang => Regex.Replace(lang, @"\s*\(.*?\)", "")) // 去掉 (xxx)
                .ToList();
            // 构建请求数据
            var requestData = cleanedLanguages.Select(lang => new { text = lang }).ToArray();

            // API端点
            string url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&to=en";

            // 使用提供的API密钥或默认密钥
            string subscriptionKey = apiKey ?? "BkByucMauiBNqkwB9PPNT26kUsr85aYwe1TlFK8ZxSv9Ray1bfkYJQQJ99BHACYeBjFXJ3w3AAAbACOGHJTO";

            // 生成10位随机数作为ClientTraceId
            string clientTraceId = Guid.NewGuid().ToString("N")[..10];

            using (var httpClient = new HttpClient())
            {
                // 设置请求头
                httpClient.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey);
                httpClient.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Region", "eastus");
                httpClient.DefaultRequestHeaders.Add("X-ClientTraceId", clientTraceId);

                // 序列化请求数据
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                // 发送请求
                var response = await httpClient.PostAsync(url, content);
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseContent = await response.Content.ReadAsStringAsync();
                var translationResults = JsonSerializer.Deserialize<JsonElement[]>(responseContent);

                // 构建映射字典
                for (int i = 0; i < translationResults.Length && i < languages.Count; i++)
                {
                    var original = languages[i];
                    var translationArray = translationResults[i].GetProperty("translations");
                    if (translationArray.GetArrayLength() > 0)
                    {
                        var translated = translationArray[0].GetProperty("text").GetString();
                        if (!string.IsNullOrEmpty(translated))
                        {
                            translations[original] = translated;
                        }
                    }
                }
            }
        }
        catch (Exception)
        {
            // 翻译失败时返回空字典
        }

        return translations;
    }

    static AutomationElement FindLiveCaptionsWindow()
    {
        var processes = Process.GetProcessesByName("LiveCaptions");
        if (processes.Length == 0)
        {
            return null;
        }

        foreach (var process in processes)
        {
            try
            {
                var window = AutomationElement.FromHandle(process.MainWindowHandle);
                if (window != null)
                {
                    return window;
                }
            }
            catch
            {
                continue;
            }
        }

        return null;
    }

    static AutomationElement FindElementByAutomationId(AutomationElement parent, string automationId)
    {
        try
        {
            var condition = new PropertyCondition(AutomationElement.AutomationIdProperty, automationId);
            return parent.FindFirst(TreeScope.Descendants, condition);
        }
        catch
        {
            return null;
        }
    }

    static AutomationElement FindElementByClassName(AutomationElement parent, string className)
    {
        try
        {
            var condition = new PropertyCondition(AutomationElement.ClassNameProperty, className);
            return parent.FindFirst(TreeScope.Descendants, condition);
        }
        catch
        {
            return null;
        }
    }

    static void InvokeElement(AutomationElement element)
    {
        try
        {
            if (element.TryGetCurrentPattern(InvokePattern.Pattern, out object pattern))
            {
                ((InvokePattern)pattern).Invoke();
            }
        }
        catch
        {
            // 静默处理异常
        }
    }

    static void ExpandComboBox(AutomationElement comboBox)
    {
        try
        {
            if (comboBox.TryGetCurrentPattern(ExpandCollapsePattern.Pattern, out object pattern))
            {
                ((ExpandCollapsePattern)pattern).Expand();
            }
        }
        catch
        {
            // 静默处理异常
        }
    }

    static void CollapseComboBox(AutomationElement comboBox)
    {
        try
        {
            if (comboBox.TryGetCurrentPattern(ExpandCollapsePattern.Pattern, out object pattern))
            {
                ((ExpandCollapsePattern)pattern).Collapse();
            }
        }
        catch
        {
            // 静默处理异常
        }
    }

    static List<string> GetListItems(AutomationElement comboBox)
    {
        var items = new List<string>();
        try
        {
            var condition = new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ListItem);
            var listItems = comboBox.FindAll(TreeScope.Descendants, condition);

            foreach (AutomationElement item in listItems)
            {
                try
                {
                    var text = item.Current.Name;
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        items.Add(text.Trim());
                    }
                }
                catch
                {
                    continue;
                }
            }
        }
        catch
        {
            // 静默处理异常
        }
        return items;
    }

    static void CleanupUI(AutomationElement languageComboBox, AutomationElement window)
    {
        try
        {
            // 关闭下拉列表
            CollapseComboBox(languageComboBox);
            Thread.Sleep(100);

            // 尝试点击继续按钮
            var continueButton = FindElementByAutomationId(window, "ContinueButton");
            if (continueButton != null)
            {
                InvokeElement(continueButton);
                Thread.Sleep(100);
            }
            else
            {
                SendSingleKey(VK_RETURN);
                Thread.Sleep(100);
            }

            // 按ESC键关闭菜单
            SendSingleKey(VK_ESCAPE);
            Thread.Sleep(100);

            // 最后尝试点击设置按钮关闭菜单
            var settingsButton = FindElementByAutomationId(window, "SettingsButton");
            if (settingsButton != null)
            {
                InvokeElement(settingsButton);
            }
        }
        catch
        {
            // 静默处理异常
        }
    }

    // Helper methods for sending keyboard input
    static void SendSingleKey(byte key)
    {
        keybd_event(key, 0, 0, UIntPtr.Zero); // Key down
        keybd_event(key, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up
    }

    static void SendKey(byte modifier, byte key)
    {
        keybd_event(modifier, 0, 0, UIntPtr.Zero); // Modifier down
        keybd_event(key, 0, 0, UIntPtr.Zero); // Key down
        keybd_event(key, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Key up
        keybd_event(modifier, 0, KEYEVENTF_KEYUP, UIntPtr.Zero); // Modifier up
    }

    // ========== 语言设置专用键盘函数 ==========
    static void SendEscapeKey()
    {
        SendSingleKey(VK_ESCAPE);
    }

    static void SendEnterKey()
    {
        SendSingleKey(VK_RETURN);
    }

    static void SendHomeKey()
    {
        SendSingleKey(0x24); // VK_HOME
    }

    static void SendPageDownKey()
    {
        SendSingleKey(VK_PRIOR); // VK_PRIOR is Page Down
    }

    static void SendDownArrowKeys(int count)
    {
        for (int i = 0; i < count; i++)
        {
            SendSingleKey(0x28); // VK_DOWN
            // Thread.Sleep(10); // 去掉短延迟
        }
    }
}