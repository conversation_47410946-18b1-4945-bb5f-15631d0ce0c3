const { spawn } = require('child_process');
const readline = require('readline');

class LiveCaptionsClient {
    constructor(exePath = './win_offline_asr.exe') {
        this.exePath = exePath;
        this.process = null;
        this.isConnected = false;
        this.callbacks = new Map();
        this.requestId = 0;
    }

    // 启动server模式
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                this.process = spawn(this.exePath, ['server'], {
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                // 设置输入输出编码
                this.process.stdin.setDefaultEncoding('utf8');
                this.process.stdout.setEncoding('utf8');
                this.process.stderr.setEncoding('utf8');

                // 创建readline接口来逐行读取输出
                this.rl = readline.createInterface({
                    input: this.process.stdout,
                    crlfDelay: Infinity
                });

                // 监听输出
                this.rl.on('line', (line) => {
                    try {
                        const data = JSON.parse(line);
                        this.handleResponse(data);
                    } catch (error) {
                        console.error('解析响应失败:', error, '原始数据:', line);
                    }
                });

                // 监听错误
                this.process.stderr.on('data', (data) => {
                    console.error('C# stderr:', data.toString());
                });

                // 监听进程退出
                this.process.on('close', (code) => {
                    console.log(`C#进程退出，代码: ${code}`);
                    this.isConnected = false;
                });

                this.process.on('error', (error) => {
                    console.error('进程错误:', error);
                    reject(error);
                });

                // 等待server启动确认
                const startupTimeout = setTimeout(() => {
                    reject(new Error('启动超时'));
                }, 5000);

                this.rl.once('line', (line) => {
                    clearTimeout(startupTimeout);
                    try {
                        const data = JSON.parse(line);
                        if (data.status === 'server_started') {
                            this.isConnected = true;
                            resolve();
                        } else {
                            reject(new Error('启动失败: ' + JSON.stringify(data)));
                        }
                    } catch (error) {
                        reject(error);
                    }
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    // 处理响应
    handleResponse(data) {
        // 如果是caption更新，直接触发事件
        if (data.type === 'caption_update') {
            this.emit('captionUpdate', data.caption);
            return;
        }

        // 其他响应通过回调处理
        if (this.pendingCallback) {
            this.pendingCallback(data);
            this.pendingCallback = null;
        }
    }

    // 发送命令
    async sendCommand(command, args = []) {
        if (!this.isConnected) {
            throw new Error('未连接到server');
        }

        return new Promise((resolve, reject) => {
            const commandData = {
                command: command,
                args: args
            };

            this.pendingCallback = (response) => {
                resolve(response);
            };

            // 设置超时
            const timeout = setTimeout(() => {
                this.pendingCallback = null;
                reject(new Error('命令超时'));
            }, 10000);

            // 发送命令
            try {
                this.process.stdin.write(JSON.stringify(commandData) + '\n');
            } catch (error) {
                clearTimeout(timeout);
                this.pendingCallback = null;
                reject(error);
            }
        });
    }

    // 简化的API方法
    async getStatus() {
        return await this.sendCommand('status');
    }

    async startLiveCaptions(path = 'LiveCaptions.exe') {
        return await this.sendCommand('start', [path]);
    }

    async stopLiveCaptions() {
        return await this.sendCommand('stop');
    }

    async hideWindow() {
        return await this.sendCommand('hide');
    }

    async showWindow() {
        return await this.sendCommand('show');
    }

    async getCaption() {
        return await this.sendCommand('get_caption');
    }

    async startWatch() {
        return await this.sendCommand('start_watch');
    }

    async stopWatch() {
        return await this.sendCommand('stop_watch');
    }

    async setLanguage(lang) {
        return await this.sendCommand('set_language', ['--lang', lang]);
    }

    async listLanguages(fast = false, noTranslate = false, apiKey = null) {
        const args = [];
        if (fast) args.push('--fast');
        if (noTranslate) args.push('--no-translate');
        if (apiKey) args.push('--api-key', apiKey);
        return await this.sendCommand('list', args);
    }

    async enableMicAudio() {
        return await this.sendCommand('enable_mic_audio');
    }

    async disableMicAudio() {
        return await this.sendCommand('disable_mic_audio');
    }

    async getReadyText() {
        return await this.sendCommand('get_ready_text');
    }

    // 事件处理
    on(event, callback) {
        if (!this.eventListeners) {
            this.eventListeners = new Map();
        }
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners && this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('事件回调错误:', error);
                }
            });
        }
    }

    // 断开连接
    disconnect() {
        if (this.process) {
            this.process.kill();
            this.process = null;
        }
        this.isConnected = false;
    }
}

// 使用示例
async function example() {
    const client = new LiveCaptionsClient('./LiveCaptionsTool.exe');

    try {
        // 连接到server
        console.log('连接到LiveCaptions server...');
        await client.connect();
        console.log('已连接!');

        // 监听字幕更新
        client.on('captionUpdate', (caption) => {
            console.log('字幕更新:', caption);
        });

        // 获取状态
        const status = await client.getStatus();
        console.log('窗口状态:', status);

        // 启动LiveCaptions
        const startResult = await client.startLiveCaptions();
        console.log('启动结果:', startResult);

        // 获取当前字幕
        const caption = await client.getCaption();
        console.log('当前字幕:', caption);

        // 开始监听字幕变化
        const watchResult = await client.startWatch();
        console.log('开始监听:', watchResult);

        // 等待一段时间来接收字幕更新
        await new Promise(resolve => setTimeout(resolve, 10000));

        // 停止监听
        const stopWatchResult = await client.stopWatch();
        console.log('停止监听:', stopWatchResult);

        // 获取语言列表
        const languages = await client.listLanguages(true, true); // 快速模式，不翻译
        console.log('可用语言:', languages);

        // 设置语言
        // const setLangResult = await client.setLanguage('Chinese');
        // console.log('设置语言结果:', setLangResult);

    } catch (error) {
        console.error('错误:', error);
    } finally {
        // 断开连接
        client.disconnect();
    }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    example();
}

module.exports = LiveCaptionsClient;
