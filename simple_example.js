const LiveCaptionsClient = require('./nodejs_client_example');

// 耗时记录工具
class Timer {
    constructor() {
        this.startTime = null;
    }

    start() {
        this.startTime = Date.now();
        return this;
    }

    end() {
        if (!this.startTime) return 0;
        return Date.now() - this.startTime;
    }

    static async measure(name, asyncFn) {
        const timer = new Timer().start();
        console.log(`🔄 开始执行: ${name}`);

        try {
            const result = await asyncFn();
            const duration = timer.end();
            console.log(`✅ 完成: ${name} (耗时: ${duration}ms)`);
            return result;
        } catch (error) {
            const duration = timer.end();
            console.log(`❌ 失败: ${name} (耗时: ${duration}ms) - ${error.message}`);
            throw error;
        }
    }
}

async function simpleExample() {
    const client = new LiveCaptionsClient('./win_offline_asr.exe');
    const totalTimer = new Timer().start();

    try {
        // 1. 连接到server模式
        await Timer.measure('连接到server模式', async () => {
            return await client.connect();
        });
        console.log('📡 Server连接状态: 已建立');

        // 2. 检查状态
        const status = await Timer.measure('检查窗口状态', async () => {
            return await client.getStatus();
        });
        console.log('📊 窗口状态:', status.visible ? '可见' : '隐藏');

        // 3. 启动LiveCaptions（如果还没启动）
        const startResult = await Timer.measure('启动LiveCaptions', async () => {
            return await client.startLiveCaptions();
        });
        if (startResult.success) {
            console.log('✅ LiveCaptions已启动');
            if (startResult.ready_text) {
                console.log('📝 准备文本:', startResult.ready_text);
            }
        } else {
            console.log('❌ 启动失败');
        }

        // 4. 隐藏窗口
        const hideResult = await Timer.measure('隐藏窗口', async () => {
            return await client.hideWindow();
        });
        console.log(hideResult.success ? '✅ 窗口已隐藏' : '❌ 隐藏失败');

        // 5. 获取一次字幕
        const caption = await Timer.measure('获取当前字幕', async () => {
            return await client.getCaption();
        });
        console.log('📖 字幕内容:', caption.caption || '(无内容)');

        // 6. 开始监听字幕变化（多线程模式）
        console.log('👂 设置字幕更新回调...');

        // 设置字幕更新回调
        client.on('captionUpdate', (captionText) => {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] 🎯 字幕更新: ${captionText || '(空)'}`);
        });

        // 启动监听
        const watchResult = await Timer.measure('启动字幕监听', async () => {
            return await client.startWatch();
        });
        if (watchResult.success) {
            console.log('✅ 字幕监听已启动（多线程模式）');
            console.log('💡 现在可以继续执行其他命令...');
        }

        // 7. 在监听的同时执行其他命令
        console.log('⏰ 等待5秒，同时监听字幕...');
        const sleepTimer = new Timer().start();
        await sleep(5000);
        console.log(`⏰ 等待完成 (耗时: ${sleepTimer.end()}ms)`);

        // 8. 在监听期间获取语言列表（证明多线程工作正常）
        const languages = await Timer.measure('获取语言列表', async () => {
            return await client.listLanguages(true, true); // 快速模式，不翻译
        });
        console.log(`📋 找到 ${languages.languages.length} 种语言`);
        if (languages.languages.length > 0) {
            console.log('前5种语言:', languages.languages.slice(0, 5));
        }

        // 9. 再等待5秒
        console.log('⏰ 再等待5秒...');
        const sleepTimer2 = new Timer().start();
        await sleep(5000);
        console.log(`⏰ 第二次等待完成 (耗时: ${sleepTimer2.end()}ms)`);

        // 10. 停止监听
        const stopWatchResult = await Timer.measure('停止字幕监听', async () => {
            return await client.stopWatch();
        });
        console.log(stopWatchResult.success ? '✅ 监听已停止' : '❌ 停止失败');

        // 11. 最后再获取一次字幕
        const finalCaption = await Timer.measure('最后获取字幕', async () => {
            return await client.getCaption();
        });
        console.log('📖 最终字幕:', finalCaption.caption || '(无内容)');

        const totalDuration = totalTimer.end();
        console.log(`🎉 示例完成！总耗时: ${totalDuration}ms (${(totalDuration/1000).toFixed(2)}秒)`);

    } catch (error) {
        const totalDuration = totalTimer.end();
        console.error(`❌ 错误: ${error.message} (总耗时: ${totalDuration}ms)`);
    } finally {
        // 断开连接
        await Timer.measure('断开连接', async () => {
            client.disconnect();
            // 给一点时间让进程完全关闭
            await sleep(100);
        });

        const finalTotalDuration = totalTimer.end();
        console.log(`📊 程序总执行时间: ${finalTotalDuration}ms (${(finalTotalDuration/1000).toFixed(2)}秒)`);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行示例
if (require.main === module) {
    console.log('🎬 LiveCaptions Node.js 客户端示例 (性能测试版)');
    console.log('================================================');
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
    console.log('');
    simpleExample();
}

module.exports = simpleExample;
